
import { ApiResponse } from "~/types/api/Api";
import { SellerReport } from "~/routes/seller.report";
import { API_BASE_URL, apiRequest } from "~/utils/api";
import { format } from "date-fns"

export async function getSellerReportData(
  request?: Request,
  queryParams?: string
): Promise<ApiResponse<SellerReport[]>> {

  return {
    data: [generateMockData(), generateMockData()],
    headers: undefined,
    success: true,
    status: 200
  }

  const response = await apiRequest<SellerReport[]>(
    `${API_BASE_URL}/bc/seller/report${queryParams ? '?' + queryParams.toString() : ''}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch dashboard data");
  }
}

// Mock data generator
const generateMockData = (): SellerReport => {
  return {
    reportDate: "2025-06-30",
    sellerId: 29,
    legalEntity: "RestaurantTest2",
    restaurantName: "RestaurantTest2",
    address: "Allamadugu Villagepost , Vedurukuppam mandalam",
    payoutCycle: "Daily",
    totalOrders: 3,
    netBillValue: 691,
    grossSales: 758.05,
    totalDeductions: 587.5805,
    totalAdditions: 0,
    netPayout: 103.4195,
    deliveredOrdersBreakdown: {
      orderCount: 3,
      subtotal: 420,
      packagingCharge: 21,
      deliveryCharge: 250,
      restaurantDiscount: 0,
      netBillValue: 691,
      gstCollected: 67.05,
      gstRetained: 0,
      gstToBePaid: 67.05,
      commissionableAmount: 691,
      commissionValue: 0,
      paymentMechanismFee: 0,
      logisticsCharge: 160,
      platformTax: 0,
      tdsAmount: 7.5805,
      otherDeductions: 420,
      customerCompensation: 0,
      deliveryChargeRecovery: 0,
      creditNoteAdjustment: 0,
      debitNoteAdjustment: 0,
      promoRecoveryAdjustment: 0,
      extraInventoryAds: 0,
      cashReceived: 0,
      payLaterDeductions: 0,
      cancellationRefund: 0,
      tdsAdditions: 0,
      totalGrossSales: 758.05,
      totalDeductions: 587.5805,
      totalAdditions: 0
    },
    cancelledOrdersBreakdown: {
      orderCount: 0,
      subtotal: 0,
      packagingCharge: 0,
      deliveryCharge: 0,
      restaurantDiscount: 0,
      netBillValue: 0,
      gstCollected: 0,
      gstRetained: 0,
      gstToBePaid: 0,
      commissionableAmount: 0,
      commissionValue: 0,
      paymentMechanismFee: 0,
      logisticsCharge: 0,
      platformTax: 0,
      tdsAmount: 0,
      otherDeductions: 0,
      customerCompensation: 0,
      deliveryChargeRecovery: 0,
      creditNoteAdjustment: 0,
      debitNoteAdjustment: 0,
      promoRecoveryAdjustment: 0,
      extraInventoryAds: 0,
      cashReceived: 0,
      payLaterDeductions: 0,
      cancellationRefund: 0,
      tdsAdditions: 0,
      totalGrossSales: 0,
      totalDeductions: 0,
      totalAdditions: 0
    },
    settlement: {
      amountSettled: 103.4195,
      pendingAmount: 0,
      settlementDate: "2025-06-30"
    }
  }
}