
import { ApiResponse } from "~/types/api/Api";
import { SellerReport } from "~/routes/seller.report";
import { API_BASE_URL, apiRequest } from "~/utils/api";
import { format } from "date-fns"

export async function getSellerReportData(
  request?: Request,
  queryParams?: string
): Promise<ApiResponse<SellerReport[]>> {

  return {
    data: [generateMockData(new Date()), generateMockData(new Date())],
    headers: undefined,
    success: true,
    status: 200
  }

  const response = await apiRequest<SellerReport[]>(
    `${API_BASE_URL}/bc/seller/report${queryParams ? '?' + queryParams.toString() : ''}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch dashboard data");
  }
}

// Mock data generator
const generateMockData = (date: Date): SellerReport => {
  const baseAmount = Math.floor(Math.random() * 50000) + 20000
  const orders = Math.floor(Math.random() * 100) + 50

  const deliveredBreakdown = {
    orderCount: orders,
    subtotal: baseAmount,
    packagingCharge: Math.floor(baseAmount * 0.005),
    deliveryCharge: 0,
    restaurantDiscount: Math.floor(baseAmount * 0.15),
    netBillValue: Math.floor(baseAmount * 0.85),
    gstCollected: Math.floor(baseAmount * 0.05),
    gstRetained: Math.floor(baseAmount * 0.05),
    gstToBePaid: 0,
    commissionableAmount: Math.floor(baseAmount * 0.85),
    commissionValue: Math.floor(baseAmount * 0.25),
    paymentMechanismFee: Math.floor(baseAmount * 0.02),
    logisticsCharge: 0,
    platformTax: Math.floor(baseAmount * 0.045),
    tdsAmount: Math.floor(baseAmount * 0.01),
    otherDeductions: 0,
    customerCompensation: 0,
    deliveryChargeRecovery: 0,
    creditNoteAdjustment: 0,
    debitNoteAdjustment: 0,
    promoRecoveryAdjustment: 0,
    extraInventoryAds: 0,
    cashReceived: 0,
    payLaterDeductions: 0,
    cancellationRefund: 0,
    tdsAdditions: 0,
    totalGrossSales: Math.floor(baseAmount * 0.9),
    totalDeductions: Math.floor(baseAmount * 0.325),
    totalAdditions: 0,
  }

  const cancelledBreakdown = {
    orderCount: 0,
    subtotal: 0,
    packagingCharge: 0,
    deliveryCharge: 0,
    restaurantDiscount: 0,
    netBillValue: 0,
    gstCollected: 0,
    gstRetained: 0,
    gstToBePaid: 0,
    commissionableAmount: 0,
    commissionValue: 0,
    paymentMechanismFee: 0,
    logisticsCharge: 0,
    platformTax: 0,
    tdsAmount: 0,
    otherDeductions: 0,
    customerCompensation: 0,
    deliveryChargeRecovery: 0,
    creditNoteAdjustment: 0,
    debitNoteAdjustment: 0,
    promoRecoveryAdjustment: 0,
    extraInventoryAds: 0,
    cashReceived: 0,
    payLaterDeductions: 0,
    cancellationRefund: 0,
    tdsAdditions: 0,
    totalGrossSales: 0,
    totalDeductions: 0,
    totalAdditions: 0,
  }

  return {
    reportDate: format(date, "yyyy-MM-dd"),
    sellerId: 29,
    legalEntity: "RestaurantTest2",
    restaurantName: "RestaurantTest2",
    address: "Allamadugu Villagepost , Vedurukuppam mandalam",
    payoutCycle: "Daily",
    totalOrders: orders,
    netBillValue: deliveredBreakdown.netBillValue,
    grossSales: deliveredBreakdown.totalGrossSales,
    totalDeductions: deliveredBreakdown.totalDeductions,
    totalAdditions: deliveredBreakdown.totalAdditions,
    netPayout: Math.floor(baseAmount * 0.65),
    deliveredOrdersBreakdown: deliveredBreakdown,
    cancelledOrdersBreakdown: cancelledBreakdown,
    settlement: {
      amountSettled: Math.floor(baseAmount * 0.65),
      pendingAmount: 0,
      settlementDate: format(date, "yyyy-MM-dd"),
    }
  }
}