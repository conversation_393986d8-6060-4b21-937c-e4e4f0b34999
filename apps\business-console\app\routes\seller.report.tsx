
import { json, ActionFunction } from "@remix-run/node"
import { useFetcher } from "@remix-run/react"
import { useState, useEffect, useCallback, useRef } from "react"
import { Button } from "~/components/ui/button"
import { Calendar } from "~/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover"
import { CalendarIcon, Download, Loader2 } from "lucide-react"
import { format } from "date-fns"
import { cn } from "~/lib/utils"
import { useToast } from "~/hooks/use-toast"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { getSellerReportData } from "~/services/seller.report"
import dayjs from "dayjs"

export type SellerReport = {
  reportDate: string
  sellerId: number
  legalEntity: string
  restaurantName: string
  address: string
  payoutCycle: string
  totalOrders: number
  netBillValue: number
  grossSales: number
  totalDeductions: number
  totalAdditions: number
  netPayout: number
  deliveredOrdersBreakdown: OrderBreakdown
  cancelledOrdersBreakdown: OrderBreakdown
  settlement: {
    amountSettled: number
    pendingAmount: number
    settlementDate: string
  }
}

export type OrderBreakdown = {
  orderCount: number
  subtotal: number
  packagingCharge: number
  deliveryCharge: number
  restaurantDiscount: number
  netBillValue: number
  gstCollected: number
  gstRetained: number
  gstToBePaid: number
  commissionableAmount: number
  commissionValue: number
  paymentMechanismFee: number
  logisticsCharge: number
  platformTax: number
  tdsAmount: number
  otherDeductions: number
  customerCompensation: number
  deliveryChargeRecovery: number
  creditNoteAdjustment: number
  debitNoteAdjustment: number
  promoRecoveryAdjustment: number
  extraInventoryAds: number
  cashReceived: number
  payLaterDeductions: number
  cancellationRefund: number
  tdsAdditions: number
  totalGrossSales: number
  totalDeductions: number
  totalAdditions: number
}

type ActionIntent = "Fetch Seller Report";
interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
  data: SellerReport[];
}
export const action: ActionFunction = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent") as ActionIntent;
  const data = formData.get("data") as any;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }
  if (intent === "Fetch Seller Report") {
    const { selectedDate } = JSON.parse(data);
    // const currentTime = dayjs().format("HH:mm:ss")
    // const prevDate = dayjs(selectedDate).subtract(1, 'day').format("YYYY-MM-DD")
    // const nextDate = dayjs(selectedDate).add(1, 'day').format("YYYY-MM-DD")
    // const queryParams = `fromDate=${dayjs(`${selectedDate}T${currentTime}`).toISOString()}&toDate=${dayjs(`${nextDate}T${currentTime}`).toISOString()}`;

    try {
      const response = await getSellerReportData(request);
      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to fetch report data" }, { status: 400 })
    }
  }
  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});

export default function SellerReport() {
  const [reportData, setReportData] = useState<SellerReport[] | null>(null)
  const fetcher = useFetcher<ActionData>()
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [isLoading, setIsLoading] = useState(false)
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchReportData()
  }, [selectedDate])

  const fetchReportData = useCallback(() => {
    console.log("Refreshing Seller Report...")

    const formData = new FormData();
    formData.append("intent", "Fetch Seller Report");
    const data = { selectedDate }
    formData.append("data", JSON.stringify(data))
    fetcher.submit(formData, { method: "post" })
  }, [selectedDate])


  const handleDownloadPDF = async () => {
    if (!reportData || reportData.length === 0) return;

    try {
      setIsGeneratingPdf(true);

      // Build HTML string with embedded data
      const htmlContent = reportData.map((report) => {
        return `
          <style>
          @page {
            size: A4;
            margin: 1mm;
          }
          .page {
            width: 100%;
            font-family: 'Segoe UI', sans-serif;
            font-size: 16px;
            line-height: 1.25;
            box-sizing: border-box;
            page-break-after: always;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
          }
          .header h1 {
            font-size: 1.125em;
            margin: 0;
            font-weight: bold;
          }
          .header .logo {
            text-align: right;
            line-height: 1em;
          }
          .header .logo .m {
            color: #bc3834;
            font-weight: bold;
          }
          .header .logo .NET {
            color: #0d9488;
            font-weight: bold;
          }
          .header .logo .brih {
            font-size: 0.875em;
            color: #4b5563;
          }
          .summary {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            margin-bottom: 8px;
            font-size: 0.75em;
            border: 1px solid #d1d5db;
          }
          .summary .cell {
            border: 1px solid #d1d5db;
            padding: 4px;
          }
          .summary .label {
            font-weight: bold;
            background-color: #f9fafb;
          }
          .summary .col2 {
            grid-column: span 2;
          }
          .summary .highlight {
            border: 1px solid #d1d5db;
            padding: 6px;
            grid-column: span 2;
            font-weight: bold;
            text-align: center;
          }
          .summary .blue-50 {
            background-color: #e2f1f8;
          }
          .summary .green-50 {
            background-color: #e4f6e8;
          }
          .summary .yellow-50 {
            background-color: #fff8eb;
          }
          .details {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            margin-bottom: 8px;
            font-size: 0.75em;
            border: 1px solid #d1d5db;
          }
          .details .cell {
            border: 1px solid #d1d5db;
            padding: 4px;
          }
          .details .head {
            font-weight: bold;
            text-align: center;
          }
          .details .col2 {
            grid-column: span 2;
          }
          .details .col3 {
            grid-column: span 3;
          }
          .details .col4 {
            grid-column: span 4;
          }
          .details .col8 {
            grid-column: span 8;
          }
          .details .cyan-50 {
            background-color: #ecfeff;
          }
          .details .center {
            text-align: center;
          }
          .details .right {
            text-align: right;
          }
          .details .bold {
            font-weight: bold;
          }
          .details .blue-100 {
            background-color: #bcdef0;
          }
          .details .green-100 {
            background-color: #c1e7c7;
          }
          .details .purple-100 {
            background-color: #f3e8ff;
          }
          .details .red-100 {
            background-color: #ffc9c7;
          }
          .settlement {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            margin-bottom: 8px;
            font-size: 0.75em;
            border: 1px solid #d1d5db;
          }
          .settlement .cell {
            border: 1px solid #d1d5db;
            padding: 4px;
          }
          .settlement .orange-100 {
            background-color: #fddfb4;
          }
          .settlement .center {
            text-align: center;
          }
          .settlement .right {
            text-align: right;
          }
          .settlement .bold {
            font-weight: bold;
          }
        </style>
        <div class="page">
          <div class="header">
            <h1>Sales Report : ${report.reportDate}</h1>
            <div class="logo">
              <div>
                <span>Powered by</span>
                <span class="m">m<span class="NET">NET</span></span>
              </div>
              <div class="brih">by Brih Solutions Private Limited</div>
            </div>
          </div>

          <div class="summary">
            <div class="cell label">Seller ID</div>
            <div class="cell">${report.sellerId}</div>
            <div class="cell label">Legal Entity</div>
            <div class="cell">${report.legalEntity}</div>
            <div class="cell label">Payout Cycle</div>
            <div class="cell">${report.payoutCycle}</div>

            <div class="cell label">Restaurant</div>
            <div class="cell col2">${report.restaurantName}, Outlet</div>
            <div class="cell label">Address</div>
            <div class="cell col2">${report.address}</div>

            <div class="cell label">PAN</div>
            <div class="cell">-</div>
            <div class="cell label">GSTIN</div>
            <div class="cell">-</div>
            <div class="cell label">TAN Number</div>
            <div class="cell">-</div>

            <div class="highlight blue-50">
              Total Orders: ${report.totalOrders}
            </div>
            <div class="highlight green-50">
              Gross Sales: ${formatCurrency(report.grossSales)}
            </div>
            <div class="highlight yellow-50">
              Net Pay-out: ${formatCurrency(report.netPayout)}
            </div>
          </div>

          <div class="details">
            <div class="cell cyan-50 head">S.No.</div>
            <div class="cell cyan-50 head col3">Particular</div>
            <div class="cell cyan-50 head col2">Delivered</div>
            <div class="cell cyan-50 head col2">Cancelled</div>
            <div class="cell cyan-50 head col2">Total</div>

            <div class="cell center">1</div>
            <div class="cell col3">Subtotal (Items total)</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.subtotal)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.subtotal)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.subtotal + report.cancelledOrdersBreakdown.subtotal)}</div>

            <div class="cell center">2</div>
            <div class="cell col3">Packaging Charge</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.packagingCharge)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.packagingCharge)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.packagingCharge + report.cancelledOrdersBreakdown.packagingCharge)}</div>

            <div class="cell center">3</div>
            <div class="cell col3">Delivery charge</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.deliveryCharge)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.deliveryCharge)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.deliveryCharge + report.cancelledOrdersBreakdown.deliveryCharge)}</div>

            <div class="cell center">4</div>
            <div class="cell col3">Restaurant discount [Promo]</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.restaurantDiscount)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.restaurantDiscount)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.restaurantDiscount + report.cancelledOrdersBreakdown.restaurantDiscount)}</div>

          
            <div class="cell blue-100 bold center col8">(A) Net bill value</div>
            <div class="cell blue-100 bold right col2">${formatCurrency(report.netBillValue)}</div>

            <div class="cell center">5</div>
            <div class="cell col3">Total GST collected from customers</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.gstCollected)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.gstCollected)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.gstCollected + report.cancelledOrdersBreakdown.gstCollected)}</div>

            <div class="cell center">6</div>
            <div class="cell col3">GST retained by platform</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.gstRetained)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.gstRetained)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.gstRetained + report.cancelledOrdersBreakdown.gstRetained)}</div>

            <div class="cell center">7</div>
            <div class="cell col3">GST to be paid by restaurant</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.gstToBePaid)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.gstToBePaid)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.gstToBePaid + report.cancelledOrdersBreakdown.gstToBePaid)}</div>

            
            <div class="cell green-100 bold center col8">(B) Gross Sales</div>
            <div class="cell green-100 bold right col2">${formatCurrency(report.grossSales)}</div>

            <div class="cell center">8</div>
            <div class="cell col3">Commissionable amount</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.commissionableAmount)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.commissionableAmount)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.commissionableAmount + report.cancelledOrdersBreakdown.commissionableAmount)}</div>

            <div class="cell center">9</div>
            <div class="cell col3">Commission value</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.commissionValue)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.commissionValue)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.commissionValue + report.cancelledOrdersBreakdown.commissionValue)}</div>

            <div class="cell center">10</div>
            <div class="cell col3">Payment mechanism fee</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.paymentMechanismFee)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.paymentMechanismFee)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.paymentMechanismFee + report.cancelledOrdersBreakdown.paymentMechanismFee)}</div>

            <div class="cell center">11</div>
            <div class="cell col3">Logistics charge</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.logisticsCharge)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.logisticsCharge)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.logisticsCharge + report.cancelledOrdersBreakdown.logisticsCharge)}</div>

            <div class="cell center">12</div>
            <div class="cell col3">Taxes on platform fees</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.platformTax)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.platformTax)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.platformTax + report.cancelledOrdersBreakdown.platformTax)}</div>

            <div class="cell center">13</div>
            <div class="cell col3">Tax collected at source + TCS IGST amount</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.tdsAmount)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.tdsAmount)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.tdsAmount + report.cancelledOrdersBreakdown.tdsAmount)}</div>

            <div class="cell center">14</div>
            <div class="cell col3">TDS 194O amount</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.tdsAmount)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.tdsAmount)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.tdsAmount + report.cancelledOrdersBreakdown.tdsAmount)}</div>

            <div class="cell center">15</div>
            <div class="cell col3">Other Order level deductions</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.otherDeductions)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.otherDeductions)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.otherDeductions + report.cancelledOrdersBreakdown.otherDeductions)}</div>

            <div class="cell center"></div>
            <div class="cell col3">Customer Compensation/Recoupment</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.customerCompensation)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.customerCompensation)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.customerCompensation + report.cancelledOrdersBreakdown.customerCompensation)}</div>

            <div class="cell center"></div>
            <div class="cell col3">Delivery Charges Recovery</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.deliveryChargeRecovery)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.deliveryChargeRecovery)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.deliveryChargeRecovery + report.cancelledOrdersBreakdown.deliveryChargeRecovery)}</div>

            <div class="cell center"></div>
            <div class="cell col3">Credit note/(Debit Note) adjustment</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.creditNoteAdjustment)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.creditNoteAdjustment)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.creditNoteAdjustment + report.cancelledOrdersBreakdown.creditNoteAdjustment)}</div>

            <div class="cell center"></div>
            <div class="cell col3">Promo recovery adjustment</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.promoRecoveryAdjustment)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.promoRecoveryAdjustment)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.promoRecoveryAdjustment + report.cancelledOrdersBreakdown.promoRecoveryAdjustment)}</div>

            <div class="cell center"></div>
            <div class="cell col3">Extra Inventory Ads</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.extraInventoryAds)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.extraInventoryAds)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.extraInventoryAds + report.cancelledOrdersBreakdown.extraInventoryAds)}</div>

            <div class="cell center">16</div>
            <div class="cell col3">Amount received in cash (self delivery)</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.cashReceived)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.cashReceived)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.cashReceived + report.cancelledOrdersBreakdown.cashReceived)}</div>

            <div class="cell center">17</div>
            <div class="cell col3">Pay later deductions & other Res ID level deductions</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.payLaterDeductions)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.payLaterDeductions)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.payLaterDeductions + report.cancelledOrdersBreakdown.payLaterDeductions)}</div>

          
            <div class="cell red-100 bold center col8">(C) Total Deductions</div>
            <div class="cell red-100 bold right col2">${formatCurrency(report.totalDeductions)}</div>

            <div class="cell center">18</div>
            <div class="cell col3">Cancellation refund/Tip for kitchen staff</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.cancellationRefund)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.cancellationRefund)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.cancellationRefund + report.cancelledOrdersBreakdown.cancellationRefund)}</div>

            <div class="cell center">19</div>
            <div class="cell col3">TDS 194 H and other Res id level Additions</div>
            <div class="cell right col2">${formatCurrency(report.deliveredOrdersBreakdown.tdsAdditions)}</div>
            <div class="cell right col2">${formatCurrency(report.cancelledOrdersBreakdown.tdsAdditions)}</div>
            <div class="cell right bold col2">${formatCurrency(report.deliveredOrdersBreakdown.tdsAdditions + report.cancelledOrdersBreakdown.tdsAdditions)}</div>

            
            <div class="cell green-100 bold center col8">(D) Total Additions</div>
            <div class="cell green-100 bold right col2">${formatCurrency(report.totalAdditions)}</div>


            <div class="cell purple-100 font-bold center col8">(E) Net Payout</div>
            <div class="cell purple-100 font-bold right col2">${formatCurrency(report.netPayout)}</div>
          </div>

          <div class="settlement">
            <div class="cell orange-100 bold">Settlement Details</div>
            <div class="cell orange-100 bold center">Delivered</div>
            <div class="cell orange-100 bold center">Cancelled</div>
            <div class="cell orange-100 bold center">Total</div>

            <div class="cell">Amount Settled</div>
            <div class="cell right">${formatCurrency(report.settlement.amountSettled)}</div>
            <div class="cell right">₹0</div>
            <div class="cell right bold">${formatCurrency(report.settlement.amountSettled)}</div>

            <div class="cell">Pending Amount (Will be credited on next pay-out day)</div>
            <div class="cell right">${formatCurrency(report.settlement.pendingAmount)}</div>
            <div class="cell right">₹0</div>
            <div class="cell right bold">${formatCurrency(report.settlement.pendingAmount)}</div>
          </div>
        </div>`;
      }).join("");

      // Use native print functionality
      handlePrint(htmlContent);

    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handlePrint = (htmlContent: string) => {
    const newWindow = window.open("", "_blank");
    if (!newWindow) {
      toast({
        title: "Error",
        description: "Unable to open print window. Please check your browser's popup settings.",
        variant: "destructive",
      });
      return;
    }

    newWindow.document.write("<html><head><title>Daily Sales Report</title></head><body>");
    newWindow.document.write(htmlContent);
    newWindow.document.write("</body></html>");
    newWindow.document.close();
    newWindow.focus();

    const delayPrint = () => {
      const allImages = newWindow.document.images;
      let loadedImages = 0;

      const checkImagesLoaded = () => {
        loadedImages = Array.from(allImages).filter((img) => img.complete).length;
        if (loadedImages === allImages.length) {
          newWindow.print();
        } else {
          setTimeout(checkImagesLoaded, 50);
        }
      };

      if (allImages.length > 0) {
        checkImagesLoaded();
      } else {
        newWindow.print();
      }
    };

    delayPrint();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  useEffect(() => {
    if (fetcher.data?.intent === "Fetch Seller Report") {
      if (fetcher.data?.success) {
        fetcher.data.data ? setReportData(fetcher.data.data) : setReportData(null)
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
    if (fetcher.state === "submitting") {
      setIsLoading(true)
    } else if (fetcher.state === "idle") {
      setIsLoading(false)
    }
  }, [fetcher.data])

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Seller Report</h1>

            <div className="flex flex-col sm:flex-row gap-3">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[240px] justify-start text-left font-normal",
                      !selectedDate && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => date && setSelectedDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              <Button onClick={handleDownloadPDF} disabled={!reportData || isGeneratingPdf} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                {isGeneratingPdf ? "Generating PDF..." : "Download PDF"}
              </Button>
            </div>
          </div>
        </div>

        {/* Report Content */}
        {isLoading ? (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading report data...</p>
          </div>
        ) : reportData && reportData.length > 0 ? reportData.map((report, index) => {
          return (
            <div className="min-w-[750px] max-w-[1000px] bg-white rounded-lg shadow-sm overflow-hidden mb-2">
              <div className="p-2 pb-0" style={{ fontFamily: "Segoe UI, sans-serif" }}>
                {/* Header */}
                <div className="flex justify-between items-center mb-2">
                  <h1 className="text-lg font-bold">Sales Report : {report.reportDate}</h1>
                  <div className="text-right leading-4">
                    <div>
                      <span>Powered by&nbsp;</span>
                      <span className="text-red-600 font-bold">m</span>
                      <span className="text-teal-600 font-bold">NET</span>
                    </div>
                    <div className="text-sm text-gray-600">by Brih Solutions Private Limited</div>
                  </div>
                </div>

                {/* Summary Grid */}
                <div className="mb-2 grid grid-cols-6 text-xs border border-gray-300">
                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">Seller ID</div>
                  <div className="border border-gray-300 p-1">{report.sellerId}</div>
                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">Legal Entity</div>
                  <div className="border border-gray-300 p-1">{report.legalEntity}</div>
                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">Payout Cycle</div>
                  <div className="border border-gray-300 p-1">{report.payoutCycle}</div>

                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">Restaurant</div>
                  <div className="border border-gray-300 p-1 col-span-2">{report.restaurantName}, Outlet</div>
                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">Address</div>
                  <div className="border border-gray-300 p-1 col-span-2">{report.address}</div>

                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">PAN</div>
                  <div className="border border-gray-300 p-1">-</div>
                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">GSTIN</div>
                  <div className="border border-gray-300 p-1">-</div>
                  <div className="border border-gray-300 p-1 font-semibold bg-gray-50">TAN Number</div>
                  <div className="border border-gray-300 p-1">-</div>

                  <div className="border border-gray-300 p-1.5 col-span-2 bg-blue-50 font-bold text-center">
                    Total Orders: {report.totalOrders}
                  </div>
                  <div className="border border-gray-300 p-1.5 col-span-2 bg-green-50 font-bold text-center">
                    Gross Sales: {formatCurrency(report.grossSales)}
                  </div>
                  <div className="border border-gray-300 p-1.5 col-span-2 bg-yellow-50 font-bold text-center">
                    Net Pay-out: {formatCurrency(report.netPayout)}
                  </div>
                </div>

                {/* Detailed Breakdown Grid */}
                <div className="grid grid-cols-10 text-xs border border-gray-300">
                  {/* Header */}
                  <div className="border border-gray-300 p-1 bg-cyan-50 font-bold text-center">S.No.</div>
                  <div className="border border-gray-300 p-1 bg-cyan-50 font-bold text-center col-span-3">Particular</div>
                  <div className="border border-gray-300 p-1 bg-cyan-50 font-bold text-center col-span-2">Delivered</div>
                  <div className="border border-gray-300 p-1 bg-cyan-50 font-bold text-center col-span-2">Cancelled</div>
                  <div className="border border-gray-300 p-1 bg-cyan-50 font-bold text-center col-span-2">Total</div>

                  {/* Data Rows */}
                  <div className="border border-gray-300 p-1 text-center">1</div>
                  <div className="border border-gray-300 p-1 col-span-3">Subtotal (Items total)</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.subtotal)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.subtotal)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.subtotal + report.cancelledOrdersBreakdown.subtotal)}</div>

                  <div className="border border-gray-300 p-1 text-center">2</div>
                  <div className="border border-gray-300 p-1 col-span-3">Packaging Charge</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.packagingCharge)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.packagingCharge)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.packagingCharge + report.cancelledOrdersBreakdown.packagingCharge)}</div>

                  <div className="border border-gray-300 p-1 text-center">3</div>
                  <div className="border border-gray-300 p-1 col-span-3">Delivery charge</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.deliveryCharge)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.deliveryCharge)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.deliveryCharge + report.cancelledOrdersBreakdown.deliveryCharge)}</div>

                  <div className="border border-gray-300 p-1 text-center">4</div>
                  <div className="border border-gray-300 p-1 col-span-3">Restaurant discount [Promo]</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.restaurantDiscount)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.restaurantDiscount)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.restaurantDiscount + report.cancelledOrdersBreakdown.restaurantDiscount)}</div>

                  {/* Highlight Row */}
                  <div className="border border-gray-300 p-1 bg-blue-100 font-bold text-center col-span-8">(A) Net bill value</div>
                  <div className="border border-gray-300 p-1 bg-blue-100 font-bold text-right col-span-2">{formatCurrency(report.netBillValue)}</div>

                  <div className="border border-gray-300 p-1 text-center">5</div>
                  <div className="border border-gray-300 p-1 col-span-3">Total GST collected from customers</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.gstCollected)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.gstCollected)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.gstCollected + report.cancelledOrdersBreakdown.gstCollected)}</div>

                  <div className="border border-gray-300 p-1 text-center">6</div>
                  <div className="border border-gray-300 p-1 col-span-3">GST retained by platform</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.gstRetained)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.gstRetained)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.gstRetained + report.cancelledOrdersBreakdown.gstRetained)}</div>

                  <div className="border border-gray-300 p-1 text-center">7</div>
                  <div className="border border-gray-300 p-1 col-span-3">GST to be paid by restaurant</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.gstToBePaid)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.gstToBePaid)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.gstToBePaid + report.cancelledOrdersBreakdown.gstToBePaid)}</div>

                  {/* Highlight Row */}
                  <div className="border border-gray-300 p-1 bg-green-100 font-bold text-center col-span-8">(B) Gross Sales</div>
                  <div className="border border-gray-300 p-1 bg-green-100 font-bold text-right col-span-2">{formatCurrency(report.grossSales)}</div>

                  <div className="border border-gray-300 p-1 text-center">8</div>
                  <div className="border border-gray-300 p-1 col-span-3">Commissionable amount</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.commissionableAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.commissionableAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.commissionableAmount + report.cancelledOrdersBreakdown.commissionableAmount)}</div>

                  <div className="border border-gray-300 p-1 text-center">9</div>
                  <div className="border border-gray-300 p-1 col-span-3">Commission value</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.commissionValue)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.commissionValue)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.commissionValue + report.cancelledOrdersBreakdown.commissionValue)}</div>

                  <div className="border border-gray-300 p-1 text-center">10</div>
                  <div className="border border-gray-300 p-1 col-span-3">Payment mechanism fee</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.paymentMechanismFee)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.paymentMechanismFee)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.paymentMechanismFee + report.cancelledOrdersBreakdown.paymentMechanismFee)}</div>

                  <div className="border border-gray-300 p-1 text-center">11</div>
                  <div className="border border-gray-300 p-1 col-span-3">Logistics charge</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.logisticsCharge)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.logisticsCharge)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.logisticsCharge + report.cancelledOrdersBreakdown.logisticsCharge)}</div>

                  <div className="border border-gray-300 p-1 text-center">12</div>
                  <div className="border border-gray-300 p-1 col-span-3">Taxes on platform fees</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.platformTax)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.platformTax)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.platformTax + report.cancelledOrdersBreakdown.platformTax)}</div>

                  <div className="border border-gray-300 p-1 text-center">13</div>
                  <div className="border border-gray-300 p-1 col-span-3">Tax collected at source + TCS IGST amount</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.tdsAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.tdsAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.tdsAmount + report.cancelledOrdersBreakdown.tdsAmount)}</div>

                  <div className="border border-gray-300 p-1 text-center">14</div>
                  <div className="border border-gray-300 p-1 col-span-3">TDS 194O amount</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.tdsAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.tdsAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.tdsAmount + report.cancelledOrdersBreakdown.tdsAmount)}</div>

                  <div className="border border-gray-300 p-1 text-center">15</div>
                  <div className="border border-gray-300 p-1 col-span-3">Other Order level deductions</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.otherDeductions)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.otherDeductions)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.otherDeductions + report.cancelledOrdersBreakdown.otherDeductions)}</div>

                  <div className="border border-gray-300 p-1 text-center"></div>
                  <div className="border border-gray-300 p-1 col-span-3">Customer Compensation/Recoupment</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.customerCompensation)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.customerCompensation)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.customerCompensation + report.cancelledOrdersBreakdown.customerCompensation)}</div>

                  <div className="border border-gray-300 p-1 text-center"></div>
                  <div className="border border-gray-300 p-1 col-span-3">Delivery Charges Recovery</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.deliveryChargeRecovery)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.deliveryChargeRecovery)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.deliveryChargeRecovery + report.cancelledOrdersBreakdown.deliveryChargeRecovery)}</div>

                  <div className="border border-gray-300 p-1 text-center"></div>
                  <div className="border border-gray-300 p-1 col-span-3">Credit note/(Debit Note) adjustment</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.creditNoteAdjustment)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.creditNoteAdjustment)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.creditNoteAdjustment + report.cancelledOrdersBreakdown.creditNoteAdjustment)}</div>

                  <div className="border border-gray-300 p-1 text-center"></div>
                  <div className="border border-gray-300 p-1 col-span-3">Promo recovery adjustment</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.promoRecoveryAdjustment)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.promoRecoveryAdjustment)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.promoRecoveryAdjustment + report.cancelledOrdersBreakdown.promoRecoveryAdjustment)}</div>

                  <div className="border border-gray-300 p-1 text-center"></div>
                  <div className="border border-gray-300 p-1 col-span-3">Extra Inventory Ads</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.extraInventoryAds)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.extraInventoryAds)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.extraInventoryAds + report.cancelledOrdersBreakdown.extraInventoryAds)}</div>

                  <div className="border border-gray-300 p-1 text-center">16</div>
                  <div className="border border-gray-300 p-1 col-span-3">Amount received in cash (self delivery)</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.cashReceived)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.cashReceived)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.cashReceived + report.cancelledOrdersBreakdown.cashReceived)}</div>

                  <div className="border border-gray-300 p-1 text-center">17</div>
                  <div className="border border-gray-300 p-1 col-span-3">Pay later deductions & other Res ID level deductions</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.payLaterDeductions)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.payLaterDeductions)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.payLaterDeductions + report.cancelledOrdersBreakdown.payLaterDeductions)}</div>

                  {/* Highlight Row */}
                  <div className="border border-gray-300 p-1 bg-red-100 font-bold text-center col-span-8">(C) Total Deductions</div>
                  <div className="border border-gray-300 p-1 bg-red-100 font-bold text-right col-span-2">{formatCurrency(report.totalDeductions)}</div>

                  <div className="border border-gray-300 p-1 text-center">18</div>
                  <div className="border border-gray-300 p-1 col-span-3">Cancellation refund/Tip for kitchen staff</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.cancellationRefund)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.cancellationRefund)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.cancellationRefund + report.cancelledOrdersBreakdown.cancellationRefund)}</div>

                  <div className="border border-gray-300 p-1 text-center">19</div>
                  <div className="border border-gray-300 p-1 col-span-3">TDS 194 H and other Res id level Additions</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.tdsAdditions)}</div>
                  <div className="border border-gray-300 p-1 text-right col-span-2">{formatCurrency(report.cancelledOrdersBreakdown.tdsAdditions)}</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold col-span-2">{formatCurrency(report.deliveredOrdersBreakdown.tdsAdditions + report.cancelledOrdersBreakdown.tdsAdditions)}</div>

                  {/* Highlight Row */}
                  <div className="border border-gray-300 p-1 bg-green-100 font-bold text-center col-span-8">(D) Total Additions</div>
                  <div className="border border-gray-300 p-1 bg-green-100 font-bold text-right col-span-2">{formatCurrency(report.totalAdditions)}</div>

                  {/* Highlight Row */}
                  <div className="border border-gray-300 p-1 bg-purple-100 font-bold text-center col-span-8">(E) Net Payout</div>
                  <div className="border border-gray-300 p-1 bg-purple-100 font-bold text-right col-span-2">{formatCurrency(report.netPayout)}</div>
                </div>

                {/* Settlement Grid */}
                <div className="grid grid-cols-4 text-xs mt-2 border border-gray-300">
                  {/* Header */}
                  <div className="border border-gray-300 p-1 bg-orange-100 font-bold">Settlement Details</div>
                  <div className="border border-gray-300 p-1 bg-orange-100 font-bold text-center">Delivered</div>
                  <div className="border border-gray-300 p-1 bg-orange-100 font-bold text-center">Cancelled</div>
                  <div className="border border-gray-300 p-1 bg-orange-100 font-bold text-center">Total</div>

                  {/* Data Rows */}
                  <div className="border border-gray-300 p-1">Amount Settled</div>
                  <div className="border border-gray-300 p-1 text-right">{formatCurrency(report.settlement.amountSettled)}</div>
                  <div className="border border-gray-300 p-1 text-right">₹0</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold">{formatCurrency(report.settlement.amountSettled)}</div>

                  <div className="border border-gray-300 p-1">Pending Amount (Will be credited on next pay-out day)</div>
                  <div className="border border-gray-300 p-1 text-right">{formatCurrency(report.settlement.pendingAmount)}</div>
                  <div className="border border-gray-300 p-1 text-right">₹0</div>
                  <div className="border border-gray-300 p-1 text-right font-semibold">{formatCurrency(report.settlement.pendingAmount)}</div>
                </div>
              </div>
            </div>
          );
        }) : (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <p className="text-gray-600">No report data available</p>
          </div>
        )}
      </div>
    </div >
  )
}

